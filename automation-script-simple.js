const puppeteer = require('puppeteer');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
require('dotenv').config();

async function runAutomation() {
    console.log('🚀 Iniciando automação...');

    let browser = null;
    let page = null;

    try {
        // Criar um wrapper script para contornar o problema do espaço
        console.log('📱 Criando wrapper para Chrome...');

        const fs = require('fs');
        const wrapperScript = '#!/bin/bash\n"/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" "$@"';
        fs.writeFileSync('/tmp/chrome-wrapper.sh', wrapperScript);
        await execAsync('chmod +x /tmp/chrome-wrapper.sh');

        console.log('🚀 Abrindo Chrome do Windows...');
        browser = await puppeteer.launch({
            executablePath: '/tmp/chrome-wrapper.sh',
            headless: false,
            slowMo: 1000,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-dev-shm-usage'
            ],
            defaultViewport: null
        });

        page = await browser.newPage();
        console.log('✅ Navegador aberto com sucesso!');

        // EXECUTAR AUTOMAÇÃO COMPLETA
        console.log('\n🎯 Iniciando automação do formulário...');

        // 1. Navegar para a página
        console.log('1️⃣ Abrindo URL...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/login/login.load?sessionExpired=yes&REDIRECT_TO=/pages/serviceRequestIncident/serviceRequestIncident.load#/request', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        console.log('✅ Página carregada');

        // 2. Clicar no campo usuário
        console.log('2️⃣ Clicando no campo usuário...');
        await page.waitForSelector('#user_login', { timeout: 15000 });
        await page.click('#user_login');
        console.log('✅ Campo usuário clicado');

        // 3. Digitar usuário
        console.log('3️⃣ Digitando usuário...');
        await page.type('#user_login', '1ailton.menezes', { delay: 100 });
        console.log('✅ Usuário digitado');

        // 4. Digitar senha
        console.log('4️⃣ Digitando senha...');
        await page.type('#senha', 'Ail3108@pmz', { delay: 100 });
        console.log('✅ Senha digitada');

        // 5. Clicar no botão entrar
        console.log('5️⃣ Fazendo login...');
        await page.click('#btnEntrar');

        // Aguardar navegação
        try {
            await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
            console.log('✅ Login realizado');
        } catch (navError) {
            console.log('⚠️ Timeout na navegação, aguardando mais um pouco...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }

        // Verificar se estamos logados
        const currentUrl = page.url();
        console.log(`📍 URL atual: ${currentUrl}`);

        // 6. Clicar no campo solicitante
        console.log('6️⃣ Clicando no campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 15000 });
        await page.click('#request-solicitante');
        console.log('✅ Campo solicitante clicado');

        // 7. Digitar solicitante
        console.log('7️⃣ Digitando solicitante...');
        await page.type('#request-solicitante', 'ailton pereira de menezes', { delay: 100 });
        console.log('✅ Solicitante digitado');

        // 8. Clicar no autocomplete do solicitante
        try {
            console.log('8️⃣ Aguardando e clicando no autocomplete do solicitante...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 8000 });
            await page.click('[id*="Autocomplete_"] div.selected');
            console.log('✅ Autocomplete do solicitante selecionado');
        } catch (error) {
            console.log('⚠️ Autocomplete do solicitante não encontrado, continuando...');
        }

        // 9. Clicar no campo unidade
        console.log('9️⃣ Clicando no campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        console.log('✅ Campo unidade clicado');

        // 10. Digitar unidade
        console.log('🔟 Digitando unidade...');
        await page.type('#request-unidade', 'bloco', { delay: 100 });
        console.log('✅ Unidade digitada');

        // 11. Clicar no autocomplete da unidade
        try {
            console.log('1️⃣1️⃣ Aguardando e clicando no autocomplete da unidade...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 8000 });
            await page.click('[id*="Autocomplete_"] div.selected');
            console.log('✅ Autocomplete da unidade selecionado');
        } catch (error) {
            console.log('⚠️ Autocomplete da unidade não encontrado, continuando...');
        }

        // 12. Clicar no select de origem
        console.log('1️⃣2️⃣ Clicando no select de origem...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.click('#select-request-origem-atendimento');
        console.log('✅ Select de origem clicado');

        // 13. Selecionar origem "Portal"
        console.log('1️⃣3️⃣ Selecionando origem "Portal"...');
        await page.select('#select-request-origem-atendimento', 'Portal');
        console.log('✅ Origem "Portal" selecionada');

        // 14. Clicar novamente no select de origem
        console.log('1️⃣4️⃣ Clicando novamente no select de origem...');
        await page.click('#select-request-origem-atendimento');
        console.log('✅ Select de origem clicado novamente');

        // 15. Expandir outras informações
        console.log('1️⃣5️⃣ Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 10000 });
            await page.click('#request-requester-showOtherInformations span');
            console.log('✅ Outras informações expandidas');
        } catch (error) {
            console.log('⚠️ Botão de outras informações não encontrado, continuando...');
        }

        // 16. Clicar no select de localidade
        console.log('1️⃣6️⃣ Clicando no select de localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 10000 });
            await page.click('#select-request-localidade');
            console.log('✅ Select de localidade clicado');
        } catch (error) {
            console.log('⚠️ Select de localidade não encontrado, continuando...');
        }

        // 17. Selecionar localidade
        console.log('1️⃣7️⃣ Selecionando localidade...');
        try {
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
            console.log('✅ Localidade selecionada');
        } catch (error) {
            console.log('⚠️ Não foi possível selecionar a localidade, continuando...');
        }

        // 18. Clicar novamente no select de localidade
        console.log('1️⃣8️⃣ Clicando novamente no select de localidade...');
        try {
            await page.click('#select-request-localidade');
            console.log('✅ Select de localidade clicado novamente');
        } catch (error) {
            console.log('⚠️ Não foi possível clicar novamente no select de localidade');
        }

        // 19. Clicar no último elemento
        console.log('1️⃣9️⃣ Clicando no último elemento...');
        try {
            const elements = await page.$x('//*[@id="service-request-view"]/div/div/div/div[2]/div/div/div[3]/div/div/div/fieldset/div[2]/div[4]/div[2]');
            if (elements.length > 0) {
                await elements[0].click();
                console.log('✅ Último elemento clicado');
            } else {
                console.log('⚠️ Último elemento não encontrado');
            }
        } catch (error) {
            console.log('⚠️ Erro ao clicar no último elemento:', error.message);
        }

        console.log('\n🎉 AUTOMAÇÃO CONCLUÍDA COM SUCESSO!');
        console.log('💻 O navegador permanecerá aberto para você verificar');
        console.log('🔍 Verifique se todos os campos foram preenchidos corretamente');

        // Não fechar o browser para o usuário ver o resultado

    } catch (error) {
        console.error('\n💥 Erro durante a automação:', error.message);
        console.log('🔧 O navegador permanecerá aberto para debug');

        // Não fechar o browser em caso de erro para debug
    }
}

// Executar
runAutomation().catch(console.error);
