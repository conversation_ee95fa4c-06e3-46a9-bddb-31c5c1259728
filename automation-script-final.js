const puppeteer = require('puppeteer');
require('dotenv').config();

async function runAutomation() {
    console.log('Iniciando automação...');
    
    // Usar o Puppeteer normalmente, mas com configurações otimizadas para WSL
    const browser = await puppeteer.launch({
        headless: false,
        slowMo: 1500, // Mais lento para visualizar melhor
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--disable-default-apps'
        ],
        defaultViewport: null
    });

    const page = await browser.newPage();

    try {
        console.log('Navegador aberto com sucesso!');

        // 1. Abrir a página de login
        console.log('1. Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        console.log('✓ Página carregada');

        // 2. Preencher senha primeiro (como no script original)
        console.log('2. Preenchendo senha...');
        await page.waitForSelector('#senha', { timeout: 10000 });
        await page.type('#senha', process.env.LOGIN_PASSWORD, { delay: 100 });
        console.log('✓ Senha preenchida');

        // 3. Clicar no campo de usuário
        console.log('3. Clicando no campo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME, { delay: 100 });
        console.log('✓ Usuário preenchido');

        // 4. Clicar no botão entrar
        console.log('4. Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
        console.log('✓ Login realizado');

        // 5. Preencher campo solicitante
        console.log('5. Preenchendo campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 15000 });
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto', { delay: 100 });
        console.log('✓ Solicitante preenchido');
        
        // Aguardar e clicar no autocomplete
        try {
            console.log('   Aguardando autocomplete do solicitante...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 8000 });
            await page.click('[id*="Autocomplete_"] div.selected');
            console.log('✓ Autocomplete do solicitante selecionado');
        } catch (error) {
            console.log('⚠ Autocomplete do solicitante não encontrado, continuando...');
        }

        // 6. Preencher campo unidade
        console.log('6. Preenchendo campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco', { delay: 100 });
        console.log('✓ Unidade preenchida');
        
        // Aguardar e clicar no autocomplete da unidade
        try {
            console.log('   Aguardando autocomplete da unidade...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 8000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
            console.log('✓ Autocomplete da unidade selecionado');
        } catch (error) {
            console.log('⚠ Autocomplete da unidade não encontrado, continuando...');
        }

        // 7. Selecionar origem do atendimento
        console.log('7. Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.select('#select-request-origem-atendimento', 'Portal');
        console.log('✓ Origem selecionada');

        // 8. Expandir outras informações
        console.log('8. Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 8000 });
            await page.click('#request-requester-showOtherInformations span');
            console.log('✓ Outras informações expandidas');
        } catch (error) {
            console.log('⚠ Botão de outras informações não encontrado, continuando...');
        }

        // 9. Selecionar localidade
        console.log('9. Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 8000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
            console.log('✓ Localidade selecionada');
        } catch (error) {
            console.log('⚠ Campo localidade não encontrado, continuando...');
        }

        console.log('\n🎉 AUTOMAÇÃO CONCLUÍDA COM SUCESSO!');
        console.log('O navegador permanecerá aberto para você visualizar o resultado.');
        
        // Aguardar um pouco para o usuário ver
        await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
        console.error('\n❌ Erro durante a automação:', error.message);
        
        // Tirar screenshot em caso de erro
        try {
            await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
            console.log('📸 Screenshot do erro salvo como error-screenshot.png');
        } catch (screenshotError) {
            console.log('Não foi possível tirar screenshot');
        }
        
        console.log('\n🔍 O navegador permanecerá aberto para você investigar o problema.');
        
        // Não fechar o browser em caso de erro para debug
    }
    
    // Não fechar o browser automaticamente
    console.log('\n💡 Para fechar o navegador, pressione Ctrl+C no terminal.');
}

// Executar a automação
runAutomation().catch(console.error);
