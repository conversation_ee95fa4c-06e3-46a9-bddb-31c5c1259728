# CitSmart Automation Script

Este script automatiza o processo de login e preenchimento de formulário no sistema CitSmart usando Playwright.

## Pré-requisitos

- Node.js instalado
- NPM ou Yarn

## Instalação

1. Instale as dependências:
```bash
npm install
```

2. Instale os navegadores do Playwright:
```bash
npm run install-playwright
```

## Configuração

As credenciais estão configuradas no arquivo `.env`:
- `LOGIN_USERNAME`: ailton.menezes
- `LOGIN_PASSWORD`: Ail3108@pmz

## Execução

Para executar o script:
```bash
npm start
```

## O que o script faz

1. Abre o navegador Chrome
2. Navega para a página de login do CitSmart
3. <PERSON><PERSON><PERSON> as credenciais de login
4. Faz login no sistema
5. Preenche o formulário de solicitação com os dados especificados:
   - Solicitante: "ailto"
   - Unidade: "bloco"
   - Origem: "Portal"
   - Localidade: "Subsecretaria de Tecnologia da Informação"

## Observações

- O script roda com `headless: false` para que você possa visualizar o processo
- Há delays entre as ações para garantir que a página carregue completamente
- O script aguarda elementos aparecerem antes de interagir com eles
