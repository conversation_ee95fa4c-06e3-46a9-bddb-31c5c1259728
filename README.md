# CitSmart Automation Script

Este script automatiza o processo de login e preenchimento de formulário no sistema CitSmart usando Playwright.

## Pré-requisitos

- Node.js instalado
- NPM ou Yarn

## Instalação

1. Instale as dependências:
```bash
npm install
```

2. **Para Linux/WSL** - Instale as dependências do sistema primeiro:
```bash
# Instalar dependências do sistema para Playwright
npx playwright install-deps

# Ou manualmente com apt (Ubuntu/Debian):
sudo apt update
sudo apt install -y \
    libgtk-4-1 \
    libgraphene-1.0-0 \
    libxslt1.1 \
    libwoff1 \
    libevent-2.1-7 \
    libgstreamer-plugins-base1.0-0 \
    libgstreamer-gl1.0-0 \
    libwebpdemux2 \
    libavif15 \
    libharfbuzz-icu0 \
    libenchant-2-2 \
    libsecret-1-0 \
    libhyphen0 \
    libmanette-0.2-0
```

3. Instale os navegadores do Playwright:
```bash
npm run install-playwright
```

**Nota:** Se você estiver no Windows, pode usar o PowerShell diretamente sem as dependências Linux.

## Configuração

As credenciais estão configuradas no arquivo `.env`:
- `LOGIN_USERNAME`: ailton.menezes
- `LOGIN_PASSWORD`: Ail3108@pmz

## Execução

### Opção 1: Playwright (recomendado para desktop)
```bash
npm start
```

### Opção 2: Puppeteer (melhor para WSL/Linux)
Se você tiver problemas com dependências do Playwright, use a versão Puppeteer:
```bash
npm run start-puppeteer
```

### Opção 3: Puppeteer com Chrome do Windows (Recomendado para WSL)
Para usar especificamente o Chrome instalado no Windows:
```bash
npm run start-windows-chrome
```

### Solução de problemas

Se você encontrar erros de dependências no Linux/WSL, execute:
```bash
# Instalar dependências do sistema
npx playwright install-deps

# Ou instalar Puppeteer que tem menos dependências
npm install puppeteer
npm run start-puppeteer
```

## O que o script faz

1. Abre o navegador Chrome
2. Navega para a página de login do CitSmart
3. Preenche as credenciais de login
4. Faz login no sistema
5. Preenche o formulário de solicitação com os dados especificados:
   - Solicitante: "ailto"
   - Unidade: "bloco"
   - Origem: "Portal"
   - Localidade: "Subsecretaria de Tecnologia da Informação"

## Observações

- O script roda com `headless: false` para que você possa visualizar o processo
- Há delays entre as ações para garantir que a página carregue completamente
- O script aguarda elementos aparecerem antes de interagir com eles
