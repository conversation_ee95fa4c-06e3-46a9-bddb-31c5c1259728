const puppeteer = require('puppeteer');
const { execSync } = require('child_process');
require('dotenv').config();

async function runAutomation() {
    console.log('🚀 Iniciando automação direta...');
    
    try {
        // Primeiro, vamos tentar fechar qualquer Chrome com debugging port
        try {
            console.log('Verificando portas em uso...');
            execSync('netstat -ano | findstr :9222', { stdio: 'ignore' });
            console.log('Porta 9222 em uso, tentando liberar...');
            // Se chegou aqui, a porta está em uso
        } catch (e) {
            console.log('Porta 9222 livre');
        }

        // Abrir Chrome com debugging usando execSync (síncrono)
        console.log('Abrindo Chrome do Windows com debugging...');
        
        // Usar o comando que funciona, mas de forma que não trave o terminal
        const chromeCmd = '"/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-puppeteer --no-first-run --disable-default-apps --disable-extensions';
        
        // Executar em background
        execSync(`${chromeCmd} &`, { stdio: 'ignore' });
        
        // Aguardar mais tempo para o Chrome iniciar completamente
        console.log('Aguardando Chrome inicializar completamente...');
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // Tentar conectar com retry mais robusto
        let browser = null;
        let connected = false;
        
        for (let attempt = 1; attempt <= 10; attempt++) {
            try {
                console.log(`Tentativa ${attempt}/10 de conectar...`);
                
                // Verificar se a porta está respondendo
                try {
                    const response = await fetch('http://localhost:9222/json/version');
                    if (response.ok) {
                        console.log('✓ Porta de debugging respondendo');
                    }
                } catch (fetchError) {
                    console.log(`⚠ Porta não responde ainda (tentativa ${attempt})`);
                    if (attempt < 10) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        continue;
                    }
                }
                
                browser = await puppeteer.connect({
                    browserURL: 'http://localhost:9222',
                    defaultViewport: null
                });
                
                connected = true;
                console.log('✅ Conectado com sucesso!');
                break;
                
            } catch (connectError) {
                console.log(`❌ Tentativa ${attempt} falhou: ${connectError.message}`);
                if (attempt < 10) {
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }
        }
        
        if (!connected || !browser) {
            throw new Error('Não foi possível conectar ao Chrome após 10 tentativas');
        }

        // Criar nova página
        const page = await browser.newPage();
        console.log('📄 Nova página criada');

        // AUTOMAÇÃO COMPLETA
        console.log('\n🎯 Iniciando automação do formulário...');

        // 1. Navegar para a página
        console.log('1️⃣ Navegando para a página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        console.log('✅ Página carregada');

        // 2. Preencher senha
        console.log('2️⃣ Preenchendo senha...');
        await page.waitForSelector('#senha', { timeout: 15000 });
        await page.type('#senha', process.env.LOGIN_PASSWORD, { delay: 150 });
        console.log('✅ Senha preenchida');

        // 3. Preencher usuário
        console.log('3️⃣ Preenchendo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME, { delay: 150 });
        console.log('✅ Usuário preenchido');

        // 4. Fazer login
        console.log('4️⃣ Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
        console.log('✅ Login realizado');

        // 5. Preencher solicitante
        console.log('5️⃣ Preenchendo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 15000 });
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto', { delay: 150 });
        console.log('✅ Solicitante digitado');
        
        // Aguardar e selecionar autocomplete
        try {
            console.log('   🔍 Procurando autocomplete do solicitante...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 10000 });
            await page.click('[id*="Autocomplete_"] div.selected');
            console.log('✅ Solicitante selecionado do autocomplete');
        } catch (error) {
            console.log('⚠️ Autocomplete do solicitante não encontrado');
        }

        // 6. Preencher unidade
        console.log('6️⃣ Preenchendo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco', { delay: 150 });
        console.log('✅ Unidade digitada');
        
        // Aguardar e selecionar autocomplete da unidade
        try {
            console.log('   🔍 Procurando autocomplete da unidade...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 10000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
            console.log('✅ Unidade selecionada do autocomplete');
        } catch (error) {
            console.log('⚠️ Autocomplete da unidade não encontrado');
        }

        // 7. Selecionar origem
        console.log('7️⃣ Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.select('#select-request-origem-atendimento', 'Portal');
        console.log('✅ Origem selecionada');

        // 8. Expandir outras informações
        console.log('8️⃣ Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 10000 });
            await page.click('#request-requester-showOtherInformations span');
            console.log('✅ Outras informações expandidas');
        } catch (error) {
            console.log('⚠️ Botão de outras informações não encontrado');
        }

        // 9. Selecionar localidade
        console.log('9️⃣ Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 10000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
            console.log('✅ Localidade selecionada');
        } catch (error) {
            console.log('⚠️ Campo localidade não encontrado');
        }

        console.log('\n🎉 AUTOMAÇÃO CONCLUÍDA COM SUCESSO!');
        console.log('🌟 Todos os campos foram preenchidos conforme o script original');
        console.log('💻 O Chrome permanecerá aberto para você verificar o resultado');
        
        // Não fechar o browser
        
    } catch (error) {
        console.error('\n💥 Erro durante a automação:', error.message);
        
        // Fallback final
        console.log('\n🔧 Executando fallback: abrindo Chrome na página...');
        try {
            execSync('"/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load &', { stdio: 'ignore' });
            console.log('✅ Chrome aberto na página de login');
            console.log('📋 Credenciais para usar manualmente:');
            console.log(`   👤 Usuário: ${process.env.LOGIN_USERNAME}`);
            console.log(`   🔐 Senha: ${process.env.LOGIN_PASSWORD}`);
        } catch (fallbackError) {
            console.error('❌ Erro no fallback:', fallbackError.message);
        }
    }
}

// Executar
runAutomation().catch(console.error);
