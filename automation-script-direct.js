const puppeteer = require('puppeteer');
const { execSync } = require('child_process');
require('dotenv').config();

async function runAutomation() {
    console.log('🚀 Iniciando automação direta...');

    try {
        // Usar Puppeteer padrão sem debugging port
        console.log('Abrindo navegador...');

        const browser = await puppeteer.launch({
            headless: false,
            slowMo: 1500,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--no-first-run',
                '--disable-default-apps'
            ],
            defaultViewport: null
        });

        const page = await browser.newPage();
        console.log('✅ Navegador aberto com sucesso!');

        // AUTOMAÇÃO COMPLETA
        console.log('\n🎯 Iniciando automação do formulário...');

        // 1. Navegar para a página (comando "open")
        console.log('1️⃣ Abrindo URL especificada...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/login/login.load?sessionExpired=yes&REDIRECT_TO=/pages/serviceRequestIncident/serviceRequestIncident.load#/request', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        console.log('✅ Página carregada');

        // 2. Clicar no campo usuário (comando "click")
        console.log('2️⃣ Clicando no campo usuário...');
        await page.waitForSelector('#user_login', { timeout: 15000 });
        await page.click('#user_login');
        console.log('✅ Campo usuário clicado');

        // 3. Digitar usuário (comando "type")
        console.log('3️⃣ Digitando usuário...');
        await page.type('#user_login', process.env.LOGIN_USERNAME, { delay: 150 });
        console.log('✅ Usuário digitado');

        // 4. Digitar senha (comando "type")
        console.log('4️⃣ Digitando senha...');
        await page.type('#senha', process.env.LOGIN_PASSWORD, { delay: 150 });
        console.log('✅ Senha digitada');

        // 5. Clicar no botão entrar (comando "click")
        console.log('5️⃣ Clicando no botão entrar...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
        console.log('✅ Login realizado');

        // 6. Clicar no campo solicitante (comando "click")
        console.log('6️⃣ Clicando no campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 15000 });
        await page.click('#request-solicitante');
        console.log('✅ Campo solicitante clicado');

        // 7. Digitar no campo solicitante (comando "type")
        console.log('7️⃣ Digitando "ailton" no campo solicitante...');
        await page.type('#request-solicitante', 'ailton pereira de menezes', { delay: 150 });
        console.log('✅ Solicitante digitado');

        // 8. Clicar no autocomplete do solicitante (comando "click")
        try {
            console.log('8️⃣ Clicando no autocomplete do solicitante...');
            // Aguardar qualquer autocomplete aparecer
            await page.waitForSelector('[id*="Autocomplete_"] div', { timeout: 10000 });

            // Tentar clicar no seletor específico do JSON
            try {
                await page.waitForSelector('[id*="Autocomplete_13c06"] div.selected', { timeout: 5000 });
                await page.click('[id*="Autocomplete_13c06"] div.selected');
                console.log('✅ Autocomplete específico selecionado');
            } catch (specificError) {
                // Fallback para qualquer autocomplete
                await page.click('[id*="Autocomplete_"] div.selected');
                console.log('✅ Autocomplete genérico selecionado');
            }
        } catch (error) {
            console.log('⚠️ Autocomplete do solicitante não encontrado');
        }

        // 9. Clicar no campo unidade (comando "click")
        console.log('9️⃣ Clicando no campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        console.log('✅ Campo unidade clicado');

        // 10. Digitar no campo unidade (comando "type")
        console.log('🔟 Digitando "bloco" no campo unidade...');
        await page.type('#request-unidade', 'bloco', { delay: 150 });
        console.log('✅ Unidade digitada');

        // 11. Clicar no autocomplete da unidade (comando "click")
        try {
            console.log('1️⃣1️⃣ Clicando no autocomplete da unidade...');
            // Aguardar qualquer autocomplete aparecer
            await page.waitForSelector('[id*="Autocomplete_"] div', { timeout: 10000 });

            // Tentar clicar no seletor específico do JSON
            try {
                await page.waitForSelector('[id*="Autocomplete_fee58"] div.selected', { timeout: 5000 });
                await page.click('[id*="Autocomplete_fee58"] div.selected');
                console.log('✅ Autocomplete específico selecionado');
            } catch (specificError) {
                // Fallback para qualquer autocomplete
                await page.click('[id*="Autocomplete_"] div.selected');
                console.log('✅ Autocomplete genérico selecionado');
            }
        } catch (error) {
            console.log('⚠️ Autocomplete da unidade não encontrado');
        }

        // 12. Clicar no select de origem (comando "click")
        console.log('1️⃣2️⃣ Clicando no select de origem...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.click('#select-request-origem-atendimento');
        console.log('✅ Select de origem clicado');

        // 13. Selecionar origem "Portal" (comando "select")
        console.log('1️⃣3️⃣ Selecionando origem "Portal"...');
        await page.select('#select-request-origem-atendimento', 'Portal');
        console.log('✅ Origem "Portal" selecionada');

        // 14. Clicar novamente no select de origem (comando "click")
        console.log('1️⃣4️⃣ Clicando novamente no select de origem...');
        await page.click('#select-request-origem-atendimento');
        console.log('✅ Select de origem clicado novamente');

        // 15. Expandir outras informações (comando "click")
        console.log('1️⃣5️⃣ Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 10000 });
            await page.click('#request-requester-showOtherInformations span');
            console.log('✅ Outras informações expandidas');
        } catch (error) {
            console.log('⚠️ Botão de outras informações não encontrado');
        }

        // 16. Clicar no select de localidade (comando "click")
        console.log('1️⃣6️⃣ Clicando no select de localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 10000 });
            await page.click('#select-request-localidade');
            console.log('✅ Select de localidade clicado');
        } catch (error) {
            console.log('⚠️ Select de localidade não encontrado');
        }

        // 17. Selecionar localidade (comando "select")
        console.log('1️⃣7️⃣ Selecionando localidade "Subsecretaria de Tecnologia da Informação"...');
        try {
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
            console.log('✅ Localidade selecionada');
        } catch (error) {
            console.log('⚠️ Não foi possível selecionar a localidade');
        }

        // 18. Clicar novamente no select de localidade (comando "click")
        console.log('1️⃣8️⃣ Clicando novamente no select de localidade...');
        try {
            await page.click('#select-request-localidade');
            console.log('✅ Select de localidade clicado novamente');
        } catch (error) {
            console.log('⚠️ Não foi possível clicar novamente no select de localidade');
        }

        // 19. Clicar no último elemento (comando "click")
        console.log('1️⃣9️⃣ Clicando no último elemento...');
        try {
            await page.waitForSelector('#service-request-view div.service-request-wrapper div.service-request-content div.panel-default div:nth-child(4) div:nth-child(2)', { timeout: 10000 });
            await page.click('#service-request-view div.service-request-wrapper div.service-request-content div.panel-default div:nth-child(4) div:nth-child(2)');
            console.log('✅ Último elemento clicado');
        } catch (error) {
            console.log('⚠️ Não foi possível clicar no último elemento');
            // Tentar com XPath como fallback
            try {
                await page.waitForXPath('//*[@id="service-request-view"]/div/div/div/div[2]/div/div/div[3]/div/div/div/fieldset/div[2]/div[4]/div[2]', { timeout: 5000 });
                const elements = await page.$x('//*[@id="service-request-view"]/div/div/div/div[2]/div/div/div[3]/div/div/div/fieldset/div[2]/div[4]/div[2]');
                if (elements.length > 0) {
                    await elements[0].click();
                    console.log('✅ Último elemento clicado via XPath');
                }
            } catch (xpathError) {
                console.log('⚠️ Não foi possível clicar no último elemento via XPath');
            }
        }

        console.log('\n🎉 AUTOMAÇÃO CONCLUÍDA COM SUCESSO!');
        console.log('🌟 Todos os campos foram preenchidos conforme o script original');
        console.log('💻 O Chrome permanecerá aberto para você verificar o resultado');

        // Não fechar o browser

    } catch (error) {
        console.error('\n💥 Erro durante a automação:', error.message);

        // Fallback final - abrir diretamente no Chrome do Windows
        console.log('\n🔧 Executando fallback: abrindo Chrome do Windows na página...');
        try {
            execSync('"/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" "https://atendimentosti.mds.gov.br/citsmart/login/login.load?sessionExpired=yes&REDIRECT_TO=/pages/serviceRequestIncident/serviceRequestIncident.load#/request" &', { stdio: 'ignore' });
            console.log('✅ Chrome do Windows aberto na página de login');
            console.log('📋 Credenciais para usar manualmente:');
            console.log(`   👤 Usuário: ${process.env.LOGIN_USERNAME}`);
            console.log(`   🔐 Senha: ${process.env.LOGIN_PASSWORD}`);
        } catch (fallbackError) {
            console.error('❌ Erro no fallback:', fallbackError.message);
        }
    }
}

// Executar
runAutomation().catch(console.error);
