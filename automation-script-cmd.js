const puppeteer = require('puppeteer');
require('dotenv').config();

async function runAutomation() {
    console.log('Tentando conectar ao Chrome do Windows via comando...');

    // Usar puppeteer para conectar a uma instância do Chrome já aberta
    // ou abrir uma nova usando o comando do Windows
    const browser = await puppeteer.launch({
        headless: false,
        slowMo: 1000,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--remote-debugging-port=9222',
            '--disable-dev-shm-usage',
            '--user-data-dir=/tmp/chrome-user-data'
        ],
        // Não especificar executablePath para usar o Chrome padrão do sistema
        // O Puppeteer vai tentar encontrar automaticamente
    });

    const page = await browser.newPage();

    try {
        console.log('Iniciando automação...');

        // 1. Abrir a página de login
        console.log('Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // 2. Preencher senha
        console.log('Preenchendo senha...');
        await page.waitForSelector('#senha', { timeout: 10000 });
        await page.type('#senha', process.env.LOGIN_PASSWORD);

        // 3. Clicar no campo de usuário e preencher
        console.log('Preenchendo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME);

        // 4. Clicar no botão entrar
        console.log('Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });

        // 5. Preencher campo solicitante
        console.log('Preenchendo campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 10000 });
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto');
        
        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected');
        } catch (error) {
            console.log('Autocomplete do solicitante não encontrado, continuando...');
        }

        // 6. Preencher campo unidade
        console.log('Preenchendo campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco');
        
        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
        } catch (error) {
            console.log('Autocomplete da unidade não encontrado, continuando...');
        }

        // 7. Selecionar origem do atendimento
        console.log('Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.select('#select-request-origem-atendimento', 'Portal');

        // 8. Expandir outras informações
        console.log('Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 5000 });
            await page.click('#request-requester-showOtherInformations span');
        } catch (error) {
            console.log('Botão de outras informações não encontrado, continuando...');
        }

        // 9. Selecionar localidade
        console.log('Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 5000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
        } catch (error) {
            console.log('Campo localidade não encontrado, continuando...');
        }

        console.log('Automação concluída com sucesso!');
        
        // Aguardar um pouco antes de fechar para visualizar o resultado
        await new Promise(resolve => setTimeout(resolve, 5000));

    } catch (error) {
        console.error('Erro durante a automação:', error);
        // Tirar screenshot em caso de erro
        try {
            await page.screenshot({ path: 'error-screenshot.png' });
            console.log('Screenshot salvo como error-screenshot.png');
        } catch (screenshotError) {
            console.log('Não foi possível tirar screenshot');
        }
    } finally {
        await browser.close();
    }
}

// Executar a automação
runAutomation().catch(console.error);
