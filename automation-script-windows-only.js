const puppeteer = require('puppeteer');
const { spawn } = require('child_process');
require('dotenv').config();

async function runAutomation() {
    console.log('Iniciando automação usando APENAS o Chrome do Windows...');
    
    try {
        // Abrir Chrome do Windows com debugging port usando spawn (não bloqueia)
        console.log('Abrindo Chrome do Windows...');
        
        const chromeProcess = spawn('cmd.exe', [
            '/c', 
            'start',
            '""',
            '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
            '--remote-debugging-port=9222',
            '--new-window',
            '--no-first-run',
            '--disable-default-apps'
        ], {
            detached: true,
            stdio: 'ignore'
        });

        chromeProcess.unref();
        
        // Aguardar o Chrome iniciar
        console.log('Aguardando Chrome do Windows iniciar...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Conectar ao Chrome do Windows
        console.log('Conectando ao Chrome do Windows...');
        const browser = await puppeteer.connect({
            browserURL: 'http://localhost:9222',
            defaultViewport: null
        });

        const page = await browser.newPage();
        console.log('✓ Conectado ao Chrome do Windows!');

        // Automação
        console.log('\n🚀 Iniciando automação...');

        // 1. Abrir a página de login
        console.log('1. Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        console.log('✓ Página carregada');

        // 2. Preencher senha
        console.log('2. Preenchendo senha...');
        await page.waitForSelector('#senha', { timeout: 10000 });
        await page.type('#senha', process.env.LOGIN_PASSWORD, { delay: 100 });
        console.log('✓ Senha preenchida');

        // 3. Clicar no campo de usuário e preencher
        console.log('3. Preenchendo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME, { delay: 100 });
        console.log('✓ Usuário preenchido');

        // 4. Clicar no botão entrar
        console.log('4. Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
        console.log('✓ Login realizado');

        // 5. Preencher campo solicitante
        console.log('5. Preenchendo campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 15000 });
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto', { delay: 100 });
        console.log('✓ Solicitante preenchido');
        
        // Aguardar autocomplete
        try {
            console.log('   Aguardando autocomplete do solicitante...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 8000 });
            await page.click('[id*="Autocomplete_"] div.selected');
            console.log('✓ Autocomplete do solicitante selecionado');
        } catch (error) {
            console.log('⚠ Autocomplete do solicitante não encontrado, continuando...');
        }

        // 6. Preencher campo unidade
        console.log('6. Preenchendo campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco', { delay: 100 });
        console.log('✓ Unidade preenchida');
        
        // Aguardar autocomplete da unidade
        try {
            console.log('   Aguardando autocomplete da unidade...');
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 8000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
            console.log('✓ Autocomplete da unidade selecionado');
        } catch (error) {
            console.log('⚠ Autocomplete da unidade não encontrado, continuando...');
        }

        // 7. Selecionar origem do atendimento
        console.log('7. Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.select('#select-request-origem-atendimento', 'Portal');
        console.log('✓ Origem selecionada');

        // 8. Expandir outras informações
        console.log('8. Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 8000 });
            await page.click('#request-requester-showOtherInformations span');
            console.log('✓ Outras informações expandidas');
        } catch (error) {
            console.log('⚠ Botão de outras informações não encontrado, continuando...');
        }

        // 9. Selecionar localidade
        console.log('9. Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 8000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
            console.log('✓ Localidade selecionada');
        } catch (error) {
            console.log('⚠ Campo localidade não encontrado, continuando...');
        }

        console.log('\n🎉 AUTOMAÇÃO CONCLUÍDA COM SUCESSO!');
        console.log('📱 O Chrome do Windows permanecerá aberto para você visualizar.');
        
        // Não fechar o browser - deixar aberto para o usuário
        
    } catch (error) {
        console.error('\n❌ Erro durante a automação:', error.message);
        console.log('\n💡 Tentando abrir apenas uma nova aba no Chrome do Windows...');
        
        // Fallback simples: apenas abrir a página no Chrome do Windows
        try {
            spawn('cmd.exe', [
                '/c', 
                'start',
                '""',
                '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
                'https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load'
            ], {
                detached: true,
                stdio: 'ignore'
            });
            
            console.log('✓ Chrome do Windows aberto na página de login.');
            console.log('🔧 Continue a automação manualmente com as credenciais do .env');
            
        } catch (fallbackError) {
            console.error('❌ Erro no fallback:', fallbackError.message);
        }
    }
}

// Executar a automação
runAutomation().catch(console.error);
