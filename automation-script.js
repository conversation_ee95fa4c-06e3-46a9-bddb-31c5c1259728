const { chromium } = require('playwright');
require('dotenv').config();

async function runAutomation() {
    const browser = await chromium.launch({ 
        headless: false, // Para visualizar o processo
        slowMo: 1000 // Adiciona delay entre ações
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        console.log('Iniciando automação...');

        // 1. Abrir a página de login
        console.log('Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load');
        await page.waitForLoadState('networkidle');

        // 2. Preencher senha
        console.log('Preenchendo senha...');
        await page.fill('#senha', process.env.LOGIN_PASSWORD);

        // 3. Clicar no campo de usuário
        console.log('Clicando no campo de usuário...');
        await page.click('#user_login');

        // 4. Preencher usuário
        console.log('Preenchendo usuário...');
        await page.fill('#user_login', process.env.LOGIN_USERNAME);

        // 5. Clicar no botão entrar
        console.log('Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForLoadState('networkidle');

        // 6. Preencher campo solicitante
        console.log('Preenchendo campo solicitante...');
        await page.click('#request-solicitante');
        await page.fill('#request-solicitante', 'ailto');
        
        // Aguardar autocomplete aparecer e clicar na opção
        await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 5000 });
        await page.click('[id*="Autocomplete_"] div.selected');

        // 7. Preencher campo unidade
        console.log('Preenchendo campo unidade...');
        await page.click('#request-unidade');
        await page.fill('#request-unidade', 'bloco');
        
        // Aguardar autocomplete aparecer e clicar na opção
        await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 5000 });
        await page.click('[id*="Autocomplete_"] div.selected strong');

        // 8. Selecionar origem do atendimento
        console.log('Selecionando origem do atendimento...');
        await page.selectOption('#select-request-origem-atendimento', { label: 'Portal' });

        // 9. Expandir outras informações
        console.log('Expandindo outras informações...');
        await page.click('#request-requester-showOtherInformations span');

        // 10. Selecionar localidade
        console.log('Selecionando localidade...');
        await page.selectOption('#select-request-localidade', { label: 'Subsecretaria de Tecnologia da Informação' });

        console.log('Automação concluída com sucesso!');
        
        // Aguardar um pouco antes de fechar para visualizar o resultado
        await page.waitForTimeout(3000);

    } catch (error) {
        console.error('Erro durante a automação:', error);
    } finally {
        await browser.close();
    }
}

// Executar a automação
runAutomation().catch(console.error);
