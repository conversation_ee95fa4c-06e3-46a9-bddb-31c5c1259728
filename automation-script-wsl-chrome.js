const puppeteer = require('puppeteer');
const { spawn } = require('child_process');
require('dotenv').config();

async function startChromeWindows() {
    return new Promise((resolve, reject) => {
        console.log('Iniciando Chrome do Windows...');
        
        // Usar cmd.exe para executar o Chrome do Windows
        const chromeProcess = spawn('cmd.exe', [
            '/c', 
            '"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"',
            '--remote-debugging-port=9222',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-default-apps'
        ], {
            detached: true,
            stdio: 'ignore'
        });

        chromeProcess.unref();
        
        // Aguardar um pouco para o Chrome iniciar
        setTimeout(() => {
            console.log('Chrome iniciado, tentando conectar...');
            resolve();
        }, 3000);
    });
}

async function runAutomation() {
    try {
        // Primeiro, iniciar o Chrome do Windows
        await startChromeWindows();

        // Conectar ao Chrome já em execução
        const browser = await puppeteer.connect({
            browserURL: 'http://localhost:9222',
            defaultViewport: null
        });

        const page = await browser.newPage();

        console.log('Conectado ao Chrome do Windows!');
        console.log('Iniciando automação...');

        // 1. Abrir a página de login
        console.log('Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // 2. Preencher senha
        console.log('Preenchendo senha...');
        await page.waitForSelector('#senha', { timeout: 10000 });
        await page.type('#senha', process.env.LOGIN_PASSWORD);

        // 3. Clicar no campo de usuário e preencher
        console.log('Preenchendo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME);

        // 4. Clicar no botão entrar
        console.log('Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });

        // 5. Preencher campo solicitante
        console.log('Preenchendo campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 10000 });
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto');
        
        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected');
        } catch (error) {
            console.log('Autocomplete do solicitante não encontrado, continuando...');
        }

        // 6. Preencher campo unidade
        console.log('Preenchendo campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco');
        
        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
        } catch (error) {
            console.log('Autocomplete da unidade não encontrado, continuando...');
        }

        // 7. Selecionar origem do atendimento
        console.log('Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.select('#select-request-origem-atendimento', 'Portal');

        // 8. Expandir outras informações
        console.log('Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 5000 });
            await page.click('#request-requester-showOtherInformations span');
        } catch (error) {
            console.log('Botão de outras informações não encontrado, continuando...');
        }

        // 9. Selecionar localidade
        console.log('Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 5000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
        } catch (error) {
            console.log('Campo localidade não encontrado, continuando...');
        }

        console.log('Automação concluída com sucesso!');
        
        // Aguardar um pouco antes de fechar para visualizar o resultado
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Não fechar o browser para que o usuário possa ver o resultado
        console.log('Automação finalizada. O Chrome permanecerá aberto para você visualizar.');

    } catch (error) {
        console.error('Erro durante a automação:', error);
        
        if (error.message.includes('connect')) {
            console.log('\nTentativa alternativa: executando Chrome diretamente...');
            // Se não conseguir conectar, tentar abrir diretamente
            try {
                const browser = await puppeteer.launch({
                    headless: false,
                    slowMo: 1000,
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox'
                    ]
                });
                
                const page = await browser.newPage();
                await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load');
                console.log('Chrome aberto com sucesso usando método alternativo!');
                
            } catch (altError) {
                console.error('Erro no método alternativo:', altError);
            }
        }
    }
}

// Executar a automação
runAutomation().catch(console.error);
