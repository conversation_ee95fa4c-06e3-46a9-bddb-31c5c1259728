const puppeteer = require('puppeteer');
const { execSync } = require('child_process');
require('dotenv').config();

async function runAutomation() {
    console.log('Iniciando Chrome do Windows...');

    try {
        // Primeiro, iniciar o Chrome com debugging port usando o comando que funciona
        console.log('Abrindo Chrome com debugging port...');

        // Matar qualquer instância do Chrome que possa estar rodando
        try {
            execSync('taskkill /f /im chrome.exe', { stdio: 'ignore' });
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (e) {
            // Ignorar se não houver Chrome rodando
        }

        // Iniciar Chrome com debugging
        execSync('"/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" --remote-debugging-port=9222 --no-first-run --disable-default-apps --user-data-dir=/tmp/chrome-debug &', {
            stdio: 'ignore'
        });

        // Aguardar o Chrome iniciar
        console.log('Aguardando Chrome iniciar...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Tentar conectar várias vezes
        let browser = null;
        for (let i = 0; i < 5; i++) {
            try {
                console.log(`Tentativa ${i + 1} de conectar ao Chrome...`);
                browser = await puppeteer.connect({
                    browserURL: 'http://localhost:9222',
                    defaultViewport: null
                });
                console.log('Conectado com sucesso!');
                break;
            } catch (connectError) {
                console.log(`Tentativa ${i + 1} falhou:`, connectError.message);
                if (i < 4) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }

        if (!browser) {
            throw new Error('Não foi possível conectar ao Chrome após 5 tentativas');
        }

        const page = await browser.newPage();
        console.log('Conectado com sucesso!');

        // Automação
        console.log('Iniciando automação...');

        // 1. Abrir a página de login
        console.log('Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // 2. Preencher senha
        console.log('Preenchendo senha...');
        await page.waitForSelector('#senha', { timeout: 10000 });
        await page.type('#senha', process.env.LOGIN_PASSWORD);

        // 3. Clicar no campo de usuário e preencher
        console.log('Preenchendo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME);

        // 4. Clicar no botão entrar
        console.log('Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });

        // 5. Preencher campo solicitante
        console.log('Preenchendo campo solicitante...');
        await page.waitForSelector('#request-solicitante', { timeout: 10000 });
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto');

        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected');
        } catch (error) {
            console.log('Autocomplete do solicitante não encontrado, continuando...');
        }

        // 6. Preencher campo unidade
        console.log('Preenchendo campo unidade...');
        await page.waitForSelector('#request-unidade', { timeout: 10000 });
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco');

        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
        } catch (error) {
            console.log('Autocomplete da unidade não encontrado, continuando...');
        }

        // 7. Selecionar origem do atendimento
        console.log('Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento', { timeout: 10000 });
        await page.select('#select-request-origem-atendimento', 'Portal');

        // 8. Expandir outras informações
        console.log('Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span', { timeout: 5000 });
            await page.click('#request-requester-showOtherInformations span');
        } catch (error) {
            console.log('Botão de outras informações não encontrado, continuando...');
        }

        // 9. Selecionar localidade
        console.log('Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 5000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
        } catch (error) {
            console.log('Campo localidade não encontrado, continuando...');
        }

        console.log('Automação concluída com sucesso!');
        console.log('O Chrome permanecerá aberto para você visualizar o resultado.');

        // Não fechar o browser para que o usuário possa ver o resultado

    } catch (error) {
        console.error('Erro durante a automação:', error);

        // Fallback: tentar usar o Puppeteer normalmente
        console.log('\nTentando método alternativo...');
        try {
            const browser = await puppeteer.launch({
                headless: false,
                slowMo: 1000,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-web-security'
                ]
            });

            const page = await browser.newPage();
            await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load');
            console.log('Método alternativo funcionou! Continue manualmente...');

        } catch (altError) {
            console.error('Erro no método alternativo:', altError);
        }
    }
}

// Executar a automação
runAutomation().catch(console.error);
