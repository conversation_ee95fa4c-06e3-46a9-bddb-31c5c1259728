const puppeteer = require('puppeteer');
require('dotenv').config();

async function runAutomation() {
    // Caminhos possíveis para o Chrome no Windows
    const windowsChromePaths = [
        '/mnt/c/Program Files/Google/Chrome/Application/chrome.exe',
        '/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe',
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;

    // Tentar encontrar o Chrome do Windows
    const fs = require('fs');
    for (const path of windowsChromePaths) {
        try {
            if (fs.existsSync(path)) {
                executablePath = path;
                console.log(`Chrome encontrado em: ${path}`);
                break;
            }
        } catch (error) {
            // Continuar tentando outros caminhos
        }
    }

    const launchOptions = {
        headless: false, // Para visualizar o processo
        slowMo: 1000, // Adiciona delay entre ações
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
    };

    // Se encontrou o Chrome do Windows, usar ele
    if (executablePath) {
        launchOptions.executablePath = executablePath;
        console.log('Usando Chrome do Windows');
    } else {
        console.log('Chrome do Windows não encontrado, usando navegador padrão do sistema');
    }

    const browser = await puppeteer.launch(launchOptions);

    const page = await browser.newPage();

    try {
        console.log('Iniciando automação...');

        // 1. Abrir a página de login
        console.log('Abrindo página de login...');
        await page.goto('https://atendimentosti.mds.gov.br/citsmart/pages/login/login.load', {
            waitUntil: 'networkidle2'
        });

        // 2. Preencher senha
        console.log('Preenchendo senha...');
        await page.waitForSelector('#senha');
        await page.type('#senha', process.env.LOGIN_PASSWORD);

        // 3. Clicar no campo de usuário e preencher
        console.log('Preenchendo usuário...');
        await page.click('#user_login');
        await page.type('#user_login', process.env.LOGIN_USERNAME);

        // 4. Clicar no botão entrar
        console.log('Fazendo login...');
        await page.click('#btnEntrar');
        await page.waitForNavigation({ waitUntil: 'networkidle2' });

        // 5. Preencher campo solicitante
        console.log('Preenchendo campo solicitante...');
        await page.waitForSelector('#request-solicitante');
        await page.click('#request-solicitante');
        await page.type('#request-solicitante', 'ailto');

        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected');
        } catch (error) {
            console.log('Autocomplete do solicitante não encontrado, continuando...');
        }

        // 6. Preencher campo unidade
        console.log('Preenchendo campo unidade...');
        await page.waitForSelector('#request-unidade');
        await page.click('#request-unidade');
        await page.type('#request-unidade', 'bloco');

        // Aguardar autocomplete aparecer e clicar na opção
        try {
            await page.waitForSelector('[id*="Autocomplete_"] div.selected strong', { timeout: 5000 });
            await page.click('[id*="Autocomplete_"] div.selected strong');
        } catch (error) {
            console.log('Autocomplete da unidade não encontrado, continuando...');
        }

        // 7. Selecionar origem do atendimento
        console.log('Selecionando origem do atendimento...');
        await page.waitForSelector('#select-request-origem-atendimento');
        await page.select('#select-request-origem-atendimento', 'Portal');

        // 8. Expandir outras informações
        console.log('Expandindo outras informações...');
        try {
            await page.waitForSelector('#request-requester-showOtherInformations span');
            await page.click('#request-requester-showOtherInformations span');
        } catch (error) {
            console.log('Botão de outras informações não encontrado, continuando...');
        }

        // 9. Selecionar localidade
        console.log('Selecionando localidade...');
        try {
            await page.waitForSelector('#select-request-localidade', { timeout: 5000 });
            await page.select('#select-request-localidade', 'Subsecretaria de Tecnologia da Informação');
        } catch (error) {
            console.log('Campo localidade não encontrado, continuando...');
        }

        console.log('Automação concluída com sucesso!');

        // Aguardar um pouco antes de fechar para visualizar o resultado
        await page.waitForTimeout(5000);

    } catch (error) {
        console.error('Erro durante a automação:', error);
        // Tirar screenshot em caso de erro
        await page.screenshot({ path: 'error-screenshot.png' });
    } finally {
        await browser.close();
    }
}

// Executar a automação
runAutomation().catch(console.error);
