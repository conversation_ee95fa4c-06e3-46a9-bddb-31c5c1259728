{"name": "citsmart-automation", "version": "1.0.0", "description": "Automação para sistema CitSmart", "main": "automation-script.js", "scripts": {"start": "node automation-script.js", "start-puppeteer": "node automation-script-puppeteer.js", "start-windows-chrome": "node automation-script-windows-chrome.js", "start-wsl-chrome": "node automation-script-wsl-chrome.js", "start-working": "node automation-script-working.js", "start-final": "node automation-script-final.js", "start-windows-only": "node automation-script-windows-only.js", "start-chrome": "node automation-script-simple-chrome.js", "start-direct": "node automation-script-direct.js", "start-final-working": "node automation-script-final-working.js", "start-simple": "node automation-script-cmd.js", "install-playwright": "npx playwright install"}, "dependencies": {"dotenv": "^16.3.1", "playwright": "^1.40.0", "puppeteer": "^21.11.0"}, "keywords": ["automation", "playwright", "citsmart"], "author": "", "license": "ISC"}