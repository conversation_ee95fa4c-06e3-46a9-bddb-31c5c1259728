{"name": "citsmart-automation", "version": "1.0.0", "description": "Automação para sistema CitSmart", "main": "automation-script.js", "scripts": {"start": "node automation-script.js", "start-puppeteer": "node automation-script-puppeteer.js", "install-playwright": "npx playwright install"}, "dependencies": {"dotenv": "^16.3.1", "playwright": "^1.40.0", "puppeteer": "^21.11.0"}, "keywords": ["automation", "playwright", "citsmart"], "author": "", "license": "ISC"}